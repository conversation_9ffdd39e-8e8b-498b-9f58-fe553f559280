package main

import (
	"log"
	"os"

	"goland_test/internal/middlewares"

	"github.com/gofiber/fiber/v2"
	"github.com/joho/godotenv"
)


func main() {

	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file")
	}

	PORT := os.Getenv("PORT")
	app := fiber.New(fiber.Config{
		Prefork:       true,
		CaseSensitive: true,
		StrictRouting: true,
		ServerHeader:  "Fiber",
		AppName: "Test App v1.0.1",
	})

	app.Use(middlewares.CustomCors())
	

	app.Listen(":" + string(PORT))
}
