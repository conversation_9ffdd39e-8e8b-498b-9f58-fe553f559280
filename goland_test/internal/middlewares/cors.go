package middlewares

import (
	"os"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
)

func CustomCors() fiber.Handler {
	allowOrigins := os.Getenv("CORS_ALLOW_ORIGINS")
	if allowOrigins == "" {
		allowOrigins = "*"
	}

	allowMethods := os.Getenv("CORS_ALLOW_METHODS")
	if allowMethods == "" {
		allowMethods = "GET,POST,PUT,DELETE,OPTIONS"
	}

	allowHeaders := os.Getenv("CORS_ALLOW_HEADERS")
	if allowHeaders == "" {
		allowHeaders = "Origin, Content-Type, Accept, Authorization"
	}

	allowCredentials := os.Getenv("CORS_ALLOW_CREDENTIALS") == "true"

	return cors.New(cors.Config{
		AllowOrigins:     allowOrigins,
		AllowMethods:     allowMethods,
		AllowHeaders:     allowHeaders,
		AllowCredentials: allowCredentials,
	})
}